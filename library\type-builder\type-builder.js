// @ts-check

import { WeakTuple } from "../map.js";

/**
 * @template T
 * @typedef {{ new (...args: any[]): T; prototype: T; }} ConstructorType
 */

/** @typedef {(StringConstructor | NumberConstructor | BooleanConstructor | ObjectConstructor | ArrayConstructor | SymbolConstructor | FunctionConstructor | Int8ArrayConstructor | Int16ArrayConstructor | Int32ArrayConstructor | Uint8ArrayConstructor | Uint16ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor)[]} AnyType */

/**
 * @template {Template} T
 * @typedef {T extends Template<infer I> ? I[number] : never} TemplateValue
 */

/** @template {AnyType} [T=AnyType] */
export class Template {
  /** @param {T} type */
  constructor(...type) {
    this.types = new Set(type);
  }
  /** @param {AnyType[number]} value */
  canAssign(value) {
    if(this.types.has(value)) {
      return true;
    }
    return false;
  }
  /** @type {Template} */
  static ANY = new Template(String, Number, Boolean, Object, Array, Symbol, Function);
}
export default class TypeBuilder {
  /**
   * @template {{ [ key: string ]: Template }} T
   * @template {any[]} A
   * @template {any} R
   * @param {T} templates
   * @param {(templates: T) => ((...args: A) => ConstructorType<R>)} fn
  */
  static create(templates, fn) {
    class Builder {
      static #from = fn(templates);
      /** @type {Map<WeakTuple<A>, ConstructorType<R>>} */
      static #map = new Map;

      /** @param {A} args */
      static from(...args) {
        const tuple = new WeakTuple(...args);
        let Type = this.#map.get(tuple);
        if(Type === undefined) {
          Type = this.#from(...args);
          this.#map.set(tuple, Type);
        }
        return Type;
      };

    }
    return Builder;
  }
};

const MapTemplate = TypeBuilder.create({
  T: new Template(String, Number, Symbol),
  V: Template.ANY
}, function() {
  /**
   * @param {StringConstructor | NumberConstructor | SymbolConstructor} key
   * @param {AnyType[number]} value
  */
  return function(key, value) {
    return class {
      constructor() {
        this.map = new Map();
      }
    };
  };
});

const MapStringNumber = MapTemplate.from(String, Number);
const MapStringNumber2 = MapTemplate.from(String, Number);
const map = new MapStringNumber();
console.log(map);