// @ts-check

import { elementsById } from "../library/util/dom.js";
import { main } from "../library/util/util.js";
import VariableManager from "../library/variable-manager/variable-manager.js";
import Game from "./game.js";

const $main = await main({
  version: String
});

const $E = elementsById(true);

/** @type {VariableManager<"title" | "version">} */
const $V = new VariableManager(document, true);

const version = $main.args.params.version;

$V.set("version", version);

const game = new Game;

game.start();