
/**
 * @template {new (...args: any[]) => any} T
 * @param {T} Base
*/
function createClass(Base) {
  return class MyClass1Local extends Base {};
}
const MyClass1 = createClass();

class MyClass2 {};

/** @param {MyClass1} type */ // type MyClass1 = /*unresolved*/ any
function createMyClass1Instance(type) {}
// defined as: function createMyClass1Instance(type: typeof MyClass1Local): void

/** @param {MyClass2} type */ // class MyClass2
function createMyClass2Instance(type) {}
// defined as: function createMyClass2Instance(type: MyClass2): void
