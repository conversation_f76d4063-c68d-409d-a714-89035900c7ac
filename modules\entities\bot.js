// @ts-check

import Color from "../../library/color/color.js";
import { Vec2 } from "planck";
import BaseEntity from "./base-entity.js";
import Renderer from "../renderer.js";

export class Bot extends BaseEntity {
  radius = 4;
  velocity = Vec2.zero();
  acceleration = Vec2.zero();
  #direction = super.direction;

  speed = 60;
  rotationalSpeed = 2;
  thrust = 0;

  constructor() {
    super(undefined, -Math.PI / 2);
  }

  get direction() {
    return this.#direction;
  }

  /** @param {number} angle */
  rotate(angle) {
    this.angle += angle * this.rotationalSpeed;
    this.#direction = super.direction;
  }

  update(delta) {
    const netForce = Vec2.zero();
    // console.log(theta * 180 / Math.PI, Math.cos(theta));
    // console.log((Math.atan2(diff.y, diff.x) + Math.PI) * 180 / Math.PI);
    // console.log(thruster.angle * 180 / Math.PI);
    // this.angle = 
    this.acceleration.set(netForce);
    this.velocity.add(this.acceleration.mul(delta));
    this.velocity.mul(delta * this.speed);
    // this.velocity.clamp(10 / this.speed);
    this.position.add(this.velocity);
    this.acceleration.setZero();
  }

  /** @param {Renderer} renderer */
  render(renderer) {
    renderer.regularPolygon(this.position, this.radius, 3, this.angle, Vec2.zero(), Color.dark("44"), 2, "#aaa");
  }

};