import { Vec2 } from "planck";

import { Polygon } from "./shapes.js";
import Vector2Template from "../library/math/vector.js";

const Vector = Vector2Template.from(Float32Array);

export default class Renderer {

  static defaultColor = "#fff";
  static defaultFillColor = "transparent";

  #context;

  /** @param {CanvasRenderingContext2D} context */
  constructor(context) {
    this.#context = context;
    this.#context.canvas.width = this.#context.canvas.clientWidth;
    this.#context.canvas.height = this.#context.canvas.clientHeight;
    this.canvasRect = this.#context.canvas.getBoundingClientRect();
  }

  get canvas() {
    return this.#context.canvas;
  }

  get context() {
    return this.#context;
  }

  resize() {
    this.#context.canvas.width = this.#context.canvas.parentElement.clientWidth;
    this.#context.canvas.height = this.#context.canvas.parentElement.clientHeight;
    this.canvasRect = this.#context.canvas.getBoundingClientRect();
  }

  clear() {
    this.#context.clearRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
  }

  /** @param {string} color */
  fill(color) {
    this.#context.fillStyle = color;
    this.#context.fillRect(0, 0, this.#context.canvas.width, this.#context.canvas.height);
  }

  /**
   * @param {Vec2} v1
   * @param {Vec2} v2
   */
  line(v1, v2, lineWidth = 1, color = Renderer.defaultColor) {
    this.#context.beginPath();
    this.#context.moveTo(v1.x, v1.y);
    this.#context.lineTo(v2.x, v2.y);
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.stroke();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} center
   * @param {number} radius
   */
  circle(center, radius, fillColor = Renderer.defaultFillColor, lineWidth = 0, strokeColor = Renderer.defaultColor) {
    this.#context.beginPath();
    this.#context.fillStyle = fillColor;
    this.#context.arc(center.x, center.y, radius, 0, Math.PI * 2);
    this.#context.fill();
    if(lineWidth > 0) {
      this.#context.strokeStyle = strokeColor;
      this.#context.lineWidth = lineWidth;
      this.#context.stroke();
    }
    this.#context.closePath();
  }

  /**
   * @param {Vec2} center
   * @param {Vec2} radius
   */
  ellipse(center, radius, angle = 0, color = Renderer.defaultColor) {
    this.#context.fillStyle = color;
    this.#context.ellipse(center.x, center.y, radius.x, radius.y, angle, 0, Math.PI * 2);
    this.#context.fill();
  }

  /**
   * @param {Vec2} center
   * @param {Vec2} radius
   */
  ellipseLines(center, radius, angle = 0, lineWidth = 1, color = Renderer.defaultColor) {
    this.#context.beginPath();
    this.#context.strokeStyle = color;
    this.#context.lineWidth = lineWidth;
    this.#context.ellipse(center.x, center.y, radius.x, radius.y, angle, 0, Math.PI * 2);
    this.#context.stroke();
    this.#context.closePath();
  }

  /**
   * @param {Vec2} v1
   * @param {Vec2} v2
   */
  rectangle(v1, v2, angle = 0, fillColor = Renderer.defaultColor, lineWidth = 0, strokeColor = Renderer.defaultColor) {
    this.#context.fillStyle = fillColor;
    this.#context.fillRect(v1.x, v1.y, v2.x - v1.x, v2.y - v1.y);
    if(lineWidth > 0) {
      this.#context.beginPath();
      this.#context.strokeStyle = strokeColor;
      this.#context.lineWidth = lineWidth;
      this.#context.strokeRect(v1.x, v1.y, v2.x - v1.x, v2.y - v1.y);
      this.#context.closePath();
    }
  }

  /**
   * @param {Vec2} center
   * @param {number} radius
   * @param {number} sides
   */
  regularPolygon(center, radius, sides, angleOffset = 0, transformOrigin = Vec2.zero(), fillColor = Renderer.defaultColor, lineWidth = 1, strokeColor = Renderer.defaultColor) {
    this.#context.beginPath();
    const angle = Math.PI * 2 / sides;

    for(let side = 0; side < sides; side++) {
      const pointAngle = angle * side;
      const vertex = new Vec2(Math.cos(pointAngle) * radius, Math.sin(pointAngle) * radius);
      vertex.add(center);
      const rotatedPosition = Vector.rotate(vertex, Vec2.add(center, transformOrigin), angleOffset);
      // rotatedPosition.add(this.#translate).mul(this.#zoomLevel);
      this.#context.lineTo(rotatedPosition.x, rotatedPosition.y);
    }
    const vertex = new Vec2(Math.cos(0) * radius, Math.sin(0) * radius);
    vertex.add(center);
    const rotatedPosition = Vector.rotate(vertex, Vec2.add(center, transformOrigin), angleOffset);
    // rotatedPosition.add(this.#translate).mul(this.#zoomLevel);
    this.#context.lineTo(rotatedPosition.x, rotatedPosition.y);

    this.#context.fillStyle = fillColor;
    this.#context.fill();
    if(lineWidth > 0) {
      this.#context.strokeStyle = strokeColor;
      this.#context.lineJoin = "round";
      this.#context.lineWidth = lineWidth;
      this.#context.lineCap = "round";
      this.#context.stroke();
    }
    this.#context.closePath();
  }

  /** @param {Polygon} polygon */
  polygonV(polygon, fillColor = Renderer.defaultColor, lineWidth = 1, strokeColor = Renderer.defaultColor) {
    this.#context.beginPath();

    for(const vertex of polygon.absoluteVertices) {
      // const scaledPosition = Vec2.add(vertex, this.#translate).mul(this.#zoomLevel);
      this.#context.lineTo(vertex.x, vertex.y);
    }
    // const scaledPosition = Vec2.add(polygon.vertices[0], this.#translate).mul(this.#zoomLevel);
    this.#context.lineTo(polygon.absoluteVertices[0].x, polygon.absoluteVertices[0].y);

    this.#context.fillStyle = fillColor;
    this.#context.fill();
    if(lineWidth > 0) {
      this.#context.strokeStyle = strokeColor;
      this.#context.lineJoin = "round";
      this.#context.lineWidth = lineWidth;
      this.#context.lineCap = "round";
      this.#context.stroke();
    }
    this.#context.closePath();
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Vec2} position
   */
  image(image, position = Vec2.zero()) {
    this.#context.drawImage(image, position.x, position.y, image.width, image.height);
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Rectangle} source
   * @param {Rectangle} destination
   */
  imageSourceDestination(image, source, destination) {
    this.#context.drawImage(
      image,
      source.position.x, source.position.y, source.width, source.height,
      destination.position.x, destination.position.y, destination.width, destination.height
    );
  }

  /**
   * @param {CanvasImageSource} image
   * @param {Rectangle} destination
   */
  imageDestination(image, destination) {
    this.#context.drawImage(image, destination.position.x, destination.position.y, destination.width, destination.height);
  }

};