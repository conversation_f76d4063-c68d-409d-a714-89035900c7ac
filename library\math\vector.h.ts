interface RelativeIndexable<T> {
  at(index: number): T | undefined;
  values(): ArrayIterator<number>;
}

// class A implements RelativeIndexable<number> {
//   at(index: number) {
//     return 0;
//   }
//   values() {
//     return [];
//   }
// }

type RelativeIndexableConstructor<T> = new (...args: any[]) => RelativeIndexable<T>;

// class V {
//   static type<T extends RelativeIndexableConstructor<any>>(arrayType: T = A as T) {
//     return class V0 extends arrayType {
//       constructor(...args: any[]) {
//         super();
//       }
//     };
//   }
// }

// const v = V.type(A);