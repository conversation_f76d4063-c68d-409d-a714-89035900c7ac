// @ts-check

import { Body, Box, BoxShape, CircleShape, Edge, EdgeShape, Fixture, Vec2, World } from "planck";
import { RectangleShape } from "./shapes.js";
import OrderedMap from "./library/sorted-key-map.js";

export default class PhysicsWorld extends World {

  #renderer;
  /** @type {OrderedMap<number, Fixture>} */
  fixturesMap = new OrderedMap;

  meterPixelRatio = 1 / 10;

  /** @type {Set<Body>} */
  #nonPlayerBodies = new Set;

  /** @param {Renderer} renderer */
  constructor(renderer) {
    super({ gravity: new Vec2(0, 0) });
    this.#renderer = renderer;
  }

  *bodies() {
    let body = this.getBodyList();
    while(body !== null) {
      yield body;
      body = body.getNext();
    }
  }

  /** @param {Body} body */
 static *fixturesOf(body) {
    let fixture = body.getFixtureList();
    while(fixture !== null) {
      yield fixture;
      fixture = fixture.getNext();
    }
  }

  update(delta) {
    this.step(1 / 60);
  }

  /** @param {Body} body */
  destroyNonPlayerBody(body) {
    if(!this.#nonPlayerBodies.delete(body)) {
      return false;
    }
    return this.destroyBody(body);
  }

  destroyNonPlayerBodies() {
    for(const body of this.#nonPlayerBodies) {
      this.destroyBody(body);
    }
    this.#nonPlayerBodies.clear()
  }

  /** @param {import("../../data/maps/lobby.json")["boundingLayer"]["shapes"]} shapes */
  createBodiesFrom(shapes) {
    for(const shapeData of shapes) {
      switch(shapeData.shape) {

        case "line": {
          const v1 = Vec2.mulVec2Num(shapeData.points[0], this.meterPixelRatio);
          const v2 = Vec2.mulVec2Num(shapeData.points[1], this.meterPixelRatio);
          const body = this.createBody({
            type: "static",
            position: Vec2.zero()
          });
          body.createFixture(new Edge(v1, v2), {
            filterCategoryBits: CategoryBit.BoundingLayer,
            filterMaskBits: CategoryBit.Player
          });
          this.#nonPlayerBodies.add(body);
        }

      }

    }
  }

  /** @param {import("../../data/maps/lobby.json")["items"]} items */
  createBodiesFromItems(items) {
    for(const item of items) {
      const image = new Image;
      image.src = item.texture;
      const destination = RectangleShape.from(item.rect);
      destination.position.mul(this.meterPixelRatio);
      destination.width *= this.meterPixelRatio;
      destination.height *= this.meterPixelRatio;
      const body = this.createBody({
        type: "static",
        position: Vec2.zero(),
        userData: {
          texture: {
            image,
            destination
          }
        }
      });
      this.#nonPlayerBodies.add(body);
      const center = new Vec2(item.rect.x + item.rect.width / 2, item.rect.y + item.rect.height / 2).mul(this.meterPixelRatio);
      const h = new Vec2(item.rect.width, item.rect.height).mul(this.meterPixelRatio / 2);
      const fixture = body.createFixture(new Box(h.x, h.y, center), {
        filterCategoryBits: CategoryBit.Item,
        filterMaskBits: CategoryBit.Player,
        isSensor: true
      });
      this.fixturesMap.set(Number.parseInt(item.position.y * this.meterPixelRatio), fixture);
    }
  }

  /** @param {ServerGameType["tasksList"]} interactables */
  createBodiesFromInteractables(interactables, keepBodyReference = false) {
    for(const interactableName in interactables) {
      /** @type {typeof interactables[keyof typeof interactables]} */
      const shapeData = interactables[interactableName];

      if(shapeData.completed === true) {
        continue;
      }

      const body = this.createBody({
        type: "static",
        position: Vec2.zero()
      });
      this.#nonPlayerBodies.add(body);
      if(keepBodyReference) {
        shapeData.body = body;
      }

      const userData = {
        type: "interactable",
        name: interactableName,
        interactionType: shapeData.interactionType ?? "use",
        userRole: shapeData.userRole,
        taskType: shapeData.taskType,
        contacts: new Set
      };

      const fixtureOptions = {
        isSensor: true,
        filterCategoryBits: CategoryBit.Interactable,
        filterMaskBits: CategoryBit.PlayerInteraction,
        userData
      };

      switch(shapeData.shape) {

        case "circle": {
          const center = Vec2.mulVec2Num(shapeData.points[0], this.meterPixelRatio);
          const radius = Vec2.distance(...shapeData.points);
          body.createFixture(new CircleShape(center, radius * this.meterPixelRatio), fixtureOptions);
        }

        case "rectangle": {
          const dimensions = Vec2.sub(...shapeData.points);
          const center = new Vec2(
            shapeData.points[1].x + dimensions.x / 2,
            shapeData.points[1].y + dimensions.y / 2
          ).mul(this.meterPixelRatio);
          dimensions.mul(this.meterPixelRatio / 2);
          dimensions.x = Math.abs(dimensions.x);
          dimensions.y = Math.abs(dimensions.y);
          body.createFixture(new Box(dimensions.x, dimensions.y, center), fixtureOptions);
        }

      }

    }
  }

  /** @param {Fixture} fixture */
  static fixtureUserData(fixture) {
    return fixture.getUserData() ?? null;
  }


};