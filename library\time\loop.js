// @ts-check

export default class Loop {

  /** @type {number | undefined} */
  #timeoutId;
  /** @type {number} */
  #lastFrameTime;
  #tickFrequency = Math.floor(1000 / 60);
  #lag = 0;
  #isRunning = false;

  async start() {
    if(this.#isRunning) {
      return;
    }
    this.#isRunning = true;
    this.#lastFrameTime = Date.now();
    this.#loop();
  }

  async stop() {
    clearTimeout(this.#timeoutId);
    this.#isRunning = false;
  }

  /** @param {number} delta */
  update(delta) {}

  /** gets called after `update` */
  render() {}

  #setTimeout() {
    clearTimeout(this.#timeoutId);
    this.#timeoutId = setTimeout(this.#loop, this.#tickFrequency);
  }

  #loop() {
    const currentTime = performance.now();
    const elapsedTime = currentTime - this.#lastFrameTime;
    this.#lastFrameTime = currentTime;
    this.#lag += elapsedTime;
    // console.log(this.#lag);
    // let updateIterations = Math.ceil(elapsedTime / this.#tickFrequency);
    while(this.#lag >= this.#tickFrequency) {
      const delta = this.#tickFrequency / 1000;
      this.update(delta);
      this.#lag -= this.#tickFrequency;
    }
    this.render();
    this.#setTimeout();
  }

};