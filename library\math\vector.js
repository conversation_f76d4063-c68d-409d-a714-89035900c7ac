// @ts-check
/// <reference path="vector.h.ts" />

import TypeBuilder, { Template } from "../type-builder/type-builder.js";

const Vector2Template = TypeBuilder.create(
  {
    T: new Template(Array, Int8Array, Int16Array, Int32Array, Uint8Array, Uint16Array, Uint32Array, Float32Array, Float64Array),
    S: new Template(Number)
  },
  function(templates) {
    /**
     * @param {RelativeIndexableConstructor<number>} ArrayLike
     * @param {number} extraSize
    */
    return function(ArrayLike, extraSize = 0) {
      return class Vector2 extends ArrayLike {

        static #size = Math.min(2 + extraSize, 2);

        /**
         * @param {number} x
         * @param {number} y
        */
        constructor(x, y) {
          super(Vector2.#size);
          /** `x` component */
          this[0] = x;
          /** `y` component */
          this[1] = y;
        }

        get x() {
          return this[0];
        }
        get y() {
          return this[1];
        }
        set x(value) {
          this[0] = value;
        }
        set y(value) {
          this[1] = value;
        }

        *[Symbol.iterator]() {
          yield this[0];
          yield this[1];
        }

        // values() {
        //   return /** @type {[ x: number, y: number ]} */ (/** @type {unknown} */ (this));
        // }

        /**
         * @param {number} x
         * @param {number} y
        */
        set(x, y) {
          this[0] = x;
          this[1] = y;
        }
        /** @param {Vector2} vector */
        setFrom(vector) {
          this[0] = vector[0];
          this[1] = vector[1];
        }

        /** @param {Vector2} vector */
        add(vector) {
          this[0] += vector[0];
          this[1] += vector[1];
          return this;
        }
        /** @param {Vector2} vector */
        subtract(vector) {
          this[0] -= vector[0];
          this[1] -= vector[1];
          return this;
        }
        /** @param {Vector2} vector */
        scalar(vector) {
          this[0] *= vector[0];
          this[1] *= vector[1];
          return this;
        }
        /** @param {Vector2} vector */
        divide(vector) {
          this[0] /= vector[0];
          this[1] /= vector[1];
          return this;
        }
        /** @param {Vector2} vector */
        dot(vector) {
          return this[0] * vector[0] + this[1] * vector[1];
        }
        /** @param {Vector2} vector */
        cross(vector) {
          return this[0] * vector[1] - this[1] * vector[0];
        }
        mag() {
          return Math.sqrt(this.magSq());
        }
        magSq() {
          return this[0] ** 2 + this[1] ** 2;
        }
        normalise() {
          const magnitude = this.mag();
          return this.scale(magnitude === 0 ? 0 : 1 / magnitude);
        }
        /** @param {number} value */
        scale(value) {
          this[0] *= value;
          this[1] *= value;
          return this;
        }
        invert() {
          return this.scale(-1);
        }

        copy() {
          return Vector2.from(this);
        }
        /** @param {Vector2} vector */
        sum(vector) {
          return new Vector2(this[0] + vector[0], this[1] + vector[1]);
        }
        /** @param {Vector2} vector */
        difference(vector) {
          return new Vector2(this[0] - vector[0], this[1] - vector[1]);
        }
        /** @param {number} value */
        scaled(value) {
          return new Vector2(this[0] * value, this[1] * value);
        }
        absolute() {
          return new Vector2(Math.abs(this[0]), Math.abs(this[1]));
        }
        negative() {
          return new Vector2(-this[0], -this[1]);
        }
        signVector() {
          return new Vector2(Math.sign(this[0]), Math.sign(this[1]));
        }
        normalVector() {
          return new Vector2(-this[1], this[0]).normalise();
        }

        /** @param {Vector2} vector */
        static from(vector) {
          return new Vector2(vector[0], vector[1]);
        }
        static zero() {
          return new Vector2(0, 0);
        }
        static unit() {
          return new Vector2(1, 1);
        }
        static random() {
          const dirX = Math.floor(Math.random() * 2) === 0 ? 1 : -1;
          const dirY = Math.floor(Math.random() * 2) === 0 ? 1 : -1;
          return new Vector2(Math.random() * dirX, Math.random() * dirY);
        }
        /**
         * @param {Vector2} v1
         * @param {Vector2} v2
         */
        static distanceSq(v1, v2) {
          const dy = v1[1] - v2[1];
          const dx = v1[0] - v2[0];
          return dy ** 2 + dx ** 2;
        }
        /**
         * @param {Vector2} v1
         * @param {Vector2} v2
         */
        static distance(v1, v2) {
          return Math.sqrt(Vector2.distanceSq(v1, v2));
        }
        /**
         * @param {Vector2} vector
         * @param {number} value
         */
        static scale(vector, value) {
          return Vector2.from(vector).scale(value);
        }
        /** @param {Vector2} vector */
        static normalise(vector) {
          return Vector2.from(vector).normalise();
        }
        /**
         * @param {Vector2} v1
         * @param {Vector2} v2
         */
        static add(v1, v2) {
          return new Vector2(v1[0] + v2[0], v1[1] + v2[1]);
        }
        /**
         * @param {Vector2} v1
         * @param {Vector2} v2
         */
        static subtract(v1, v2) {
          return new Vector2(v1[0] - v2[0], v1[1] - v2[1]);
        }
        /**
         * @param {Vector2} vector
         * @param {Vector2} target
         * @param {number} maxDistance
        */
        static moveTowards(vector, target, maxDistance) {
          let result = Vector2.zero();
          const dx = target[0] - vector[0];
          const dy = target[1] - vector[1];
          const value = (dx * dx) + (dy * dy);

          if ((value == 0) || ((maxDistance >= 0) && (value <= maxDistance * maxDistance)))
            result = target;

          const dist = Math.sqrt(value);

          result[0] = vector[0] + dx / dist * maxDistance;
          result[1] = vector[1] + dy / dist * maxDistance;
          return result;
        }
        /**
         * @param {number} x
         * @param {number} y
         */
        static floor(x, y) {
          return new this(Math.floor(x), Math.floor(y));
        }
        /**
         * @param {number} x
         * @param {number} y
         */
        static frozen(x, y) {
          return Object.freeze(new this(x, y));
        }
        /**
         * @param {Vector2} vector
         * @param {Vector2} origin
         * @param {number} angle
         */
        static rotate(vector, origin, angle) {
          const subtracted = this.subtract(vector, origin);
          const cosTheta = Math.cos(angle);
          const sinTheta = Math.sin(angle);
          const rotated = new this(
            subtracted.x * cosTheta - subtracted.y * sinTheta,
            subtracted.y * cosTheta + subtracted.x * sinTheta
          );
          return rotated.add(origin);
        }
        /**
         * @param {Vector2} vector
         * @param {Vector2} origin
         * @param {number} angle
         */
        static rotateRelative(vector, origin, angle) {
          const cosTheta = Math.cos(angle);
          const sinTheta = Math.sin(angle);
          const rotated = new this(
            vector.x * cosTheta - vector.y * sinTheta,
            vector.y * cosTheta + vector.x * sinTheta
          );
          return rotated.add(origin);
        }
      };
    }
  }
);

export default Vector2Template;